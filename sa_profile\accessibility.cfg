{"jobs": [{"name": "post-fs-data", "cmds": ["mkdir /data/service/el1/public/barrierfree 0711 accessibility accessibility", "mkdir /data/service/el1/public/barrierfree/accessibility_ability_manager_service 0711 accessibility accessibility", "start accessibility"]}], "services": [{"name": "accessibility", "path": ["/system/bin/sa_main", "/system/profile/accessibility.json"], "importance": -20, "uid": "accessibility", "gid": ["accessibility", "shell"], "secon": "u:r:accessibility:s0", "permission": ["ohos.permission.MANAGE_SECURE_SETTINGS", "ohos.permission.MANAGE_SETTINGS", "ohos.permission.INTERCEPT_INPUT_EVENT", "ohos.permission.GET_BUNDLE_INFO_PRIVILEGED", "ohos.permission.MANAGE_LOCAL_ACCOUNTS", "ohos.permission.INTERACT_ACROSS_LOCAL_ACCOUNTS", "ohos.permission.INJECT_INPUT_EVENT", "ohos.permission.CONNECT_ACCESSIBILITY_EXTENSION", "ohos.permission.FILTER_INPUT_EVENT", "ohos.permission.MANAGE_MOUSE_CURSOR", "ohos.permission.REPORT_RESOURCE_SCHEDULE_EVENT", "ohos.permission.ACCESS_SYSTEM_SETTINGS", "ohos.permission.UPDATE_CONFIGURATION", "ohos.permission.RECEIVER_STARTUP_COMPLETED"], "permission_acls": ["ohos.permission.INTERCEPT_INPUT_EVENT", "ohos.permission.INJECT_INPUT_EVENT", "ohos.permission.FILTER_INPUT_EVENT", "ohos.permission.MANAGE_MOUSE_CURSOR"]}]}