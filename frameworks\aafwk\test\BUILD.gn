# Copyright (C) 2022-2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")
import("../../../accessibility_manager_service.gni")
import("../../../services/test/aamstestmock.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
config("module_private_config") {
  visibility = [ ":*" ]
  visibility += [ "../../../../../arkui/ace_engine/frameworks/core/components_ng/test/pattern/ability_component:ability_component_pattern_test_ng" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }
  include_dirs = aams_mock_include_dirs
  include_dirs += [
    "../../../common/log/include",
    "../include",
    "./mock/include",
    "../../acfwk/include",
    "../../asacfwk/include",
    "../../../interfaces/innerkits/aafwk/include",
    "../../../interfaces/innerkits/acfwk/include",
    "../../../interfaces/innerkits/asacfwk/include",
    "../../../interfaces/innerkits/common/include",
    "../../../services/aams/include",
    "../../../common/interface/include",
    "../../../common/interface/include/parcel",
    "../../../services/test/mock/common",
    "../../../services/test/mock/include/extern",
    "../../../services/aams/test/mock/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_test\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]

  defines += accessibility_default_defines
}

###############################################################################
ohos_unittest("accessibility_ui_test_ability_impl_test") {
  module_out_path = module_output_path
  sources = [
    "../../../services/test/mock/mock_accessible_ability_manager_service_stub.cpp",
    "../../../services/test/mock/mock_bundle_manager.cpp",
    "../../../services/test/mock/mock_service_registry.cpp",
    "../src/accessibility_ui_test_ability_impl.cpp",
    "./mock/src/mock_accessible_ability_client_impl.cpp",
    "./mock/src/mock_accessible_ability_listener.cpp",
    "unittest/accessibility_ui_test_ability_impl_test.cpp",
  ]
  sources += aams_mock_multimodalinput_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:want",
    "ability_base:zuri",
    "ability_runtime:ability_manager",
    "ability_runtime:abilitykit_native",
    "ability_runtime:dataobs_manager",
    "ability_runtime:extension_manager",
    "bundle_framework:appexecfwk_core",
    "c_utils:utils",
    "data_share:datashare_common",
    "data_share:datashare_consumer",
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "googletest:gmock_main",
    "googletest:gtest_main",
    "hilog:libhilog",
    "init:libbeget_proxy",
    "init:libbegetutil",
    "input:libmmi-client",
    "ipc:ipc_core",
    "os_account:os_account_innerkits",
    "samgr:dynamic_cache",
    "samgr:samgr_proxy",
  ]
}

################################################################################
ohos_unittest("accessible_ability_test") {
  module_out_path = module_output_path
  sources = [
    "../../../common/interface/src/accessibility_element_operator_callback_stub.cpp",
    "../../../common/interface/src/accessible_ability_client_stub.cpp",
    "../../../common/interface/src/api_event_reporter.cpp",
    "../../../common/interface/src/parcel/accessibility_ability_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_caption_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_element_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_event_info_parcel.cpp",
    "../../../services/aams/src/accessibility_datashare_helper.cpp",
    "../../../services/aams/src/accessibility_display_manager.cpp",
    "../../../services/aams/src/accessibility_dumper.cpp",
    "../../../services/aams/src/accessibility_power_manager.cpp",
    "../../../services/aams/src/accessibility_resource_bundle_manager.cpp",
    "../../../services/aams/src/accessibility_setting_observer.cpp",
    "../../../services/aams/src/accessibility_settings.cpp",
    "../../../services/aams/src/accessibility_settings_config.cpp",
    "../../../services/aams/src/accessibility_notification_helper.cpp",
    "../../../services/aams/src/magnification_window_proxy.cpp",
    "../../../services/aams/src/msdp_manager.cpp",
    "../../../services/aams/src/accessibility_security_component_manager.cpp",
    "../../../services/aams/src/accessible_ability_manager_service.cpp",
    "../../../services/aams/src/accessible_ability_manager_service_event_handler.cpp",
    "../../../services/aams/src/utils.cpp",
    "../../../services/aams/src/window_magnification_gesture.cpp",
    "../../../services/aams/src/element_operator_manager.cpp",
    "../../../services/aams/src/accessibility_ability_manager.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_account_data.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_common_event.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_event_transmission.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_input_interceptor.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_setting_provider.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_short_key.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_short_key_dialog.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_window_connection.cpp",
    "../../../services/aams/test/mock/src/mock_accessibility_window_manager.cpp",
    "../../../services/aams/test/mock/src/mock_accessible_ability_connection.cpp",
    "../../../services/aams/test/mock/src/mock_accessible_ability_manager_service_state_observer_proxy.cpp",
    "../../../services/aams/test/mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "../../../services/aams/test/mock/src/mock_full_screen_magnification_manager.cpp",
    "../../../services/aams/test/mock/src/mock_magnification_manager.cpp",
    "../../../services/aams/test/mock/src/mock_magnification_menu_manager.cpp",
    "../../../services/aams/test/mock/src/mock_window_magnification_manager.cpp",
    "../../../services/test/mock/mock_os_account_manager.cpp",
    "../../../services/test/mock/mock_parameter.c",
    "../../common/src/accessibility_caption.cpp",
    "../../common/src/accessibility_constants.cpp",
    "../../common/src/accessibility_element_info.cpp",
    "../../common/src/accessibility_event_info.cpp",
    "../../common/src/accessibility_gesture_inject_path.cpp",
    "../../common/src/accessibility_window_info.cpp",
    "../src/accessibility_element_operator_callback_impl.cpp",
    "../src/accessibility_ui_test_ability_impl.cpp",
    "../src/accessible_ability_channel_client.cpp",
    "../src/accessible_ability_client_impl.cpp",
    "./mock/src/mock_accessible_ability_channel_proxy.cpp",
    "./mock/src/mock_accessible_ability_channel_stub.cpp",
    "./mock/src/mock_accessible_ability_listener.cpp",
    "unittest/accessibility_element_operator_callback_impl_test.cpp",
    "unittest/accessible_ability_channel_client_test.cpp",
    "unittest/accessible_ability_client_impl_test.cpp",
  ]
  sources += aams_mock_multimodalinput_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:want",
    "ability_base:zuri",
    "ability_runtime:ability_manager",
    "ability_runtime:abilitykit_native",
    "ability_runtime:dataobs_manager",
    "ability_runtime:extension_manager",
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "bundle_framework:appexecfwk_base",
    "bundle_framework:appexecfwk_core",
    "c_utils:utils",
    "data_share:datashare_common",
    "data_share:datashare_consumer",

    "e2fsprogs:libext2_uuid",
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "googletest:gmock_main",
    "googletest:gtest_main",
    "graphic_2d:2d_graphics",
    "graphic_2d:librender_service_client",
    "graphic_2d:librender_service_base",
    "hicollie:libhicollie",
    "hilog:libhilog",
    "hisysevent:libhisysevent",
    "hitrace:hitrace_meter",
    "i18n:intl_util",
    "init:libbeget_proxy",
    "init:libbegetutil",
    "input:libmmi-client",
    "ipc:ipc_core",
    "memmgr:memmgrclient",
    "os_account:os_account_innerkits",
    "power_manager:powermgr_client",
    "resource_management:global_resmgr",
    "safwk:system_ability_fwk",
    "samgr:samgr_proxy",
    "window_manager:libdm",
    "window_manager:libwm",
  ]

  if (product_name != "qemu-arm-linux-min") {
    external_deps += [ "hiappevent:hiappevent_innerapi" ]
  }

  if (accessibility_feature_display_manager) {
    external_deps += [ "display_manager:displaymgr" ]
  }

  if (security_component_enable) {
    external_deps += [ "security_component_manager:libsecurity_component_sdk" ]
  }
}

###############################################################################

group("unittest") {
  testonly = true
  deps = []

  deps += [
    ":accessibility_ui_test_ability_impl_test",
    ":accessible_ability_test",
  ]
}
###############################################################################
