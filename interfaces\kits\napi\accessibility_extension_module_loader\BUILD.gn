# Copyright (C) 2022-2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("../../../../accessibility_manager_service.gni")

config("accessibility_extension_module_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "../../../../common/log/include",
    "../../../../common/interface/include",
    "../../../innerkits/acfwk/include",
    "include",
    "../include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_napi\"",
    "AAMS_LOG_DOMAIN = 0xD001D10",
  ]

  defines += accessibility_default_defines

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }
}

ohos_shared_library("accessibility_extension_module") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = false
    cfi_cross_dso = false
    debug = false
  }

  sources = [
    "src/accessibility_extension.cpp",
    "src/accessibility_extension_context.cpp",
    "src/accessibility_extension_module_loader.cpp",
    "src/napi_accessibility_element.cpp",
    "src/napi_accessibility_extension.cpp",
    "src/napi_accessibility_extension_context.cpp",
    "src/napi_utils.cpp",
    "../src/napi_accessibility_utils.cpp"
  ]

  configs = [ ":accessibility_extension_module_config" ]

  deps = [
    "../../../../common/interface:accessibility_interface",
    "../../../innerkits/aafwk:accessibleability",
    "./../../../innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:want",
    "ability_runtime:ability_context_native",
    "ability_runtime:ability_manager",
    "ability_runtime:abilitykit_native",
    "ability_runtime:app_context",
    "ability_runtime:extensionkit_native",
    "ability_runtime:napi_common",
    "ability_runtime:runtime",
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "c_utils:utils",
    "common_event_service:cesfwk_innerkits",
    "ffrt:libffrt",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_core",
    "ipc:ipc_napi",
    "napi:ace_napi",
  ]

  relative_install_dir = "extensionability/"
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
