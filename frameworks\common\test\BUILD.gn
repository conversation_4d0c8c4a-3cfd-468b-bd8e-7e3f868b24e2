# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
config("module_private_config") {
  visibility = [ ":*" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }
  include_dirs = [
    "../../../common/log/include",
    "../../../interfaces/innerkits/common/include",
  ]
  defines = [
    "AAMS_LOG_TAG = \"accessibility_test\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]
}

ohos_unittest("accessibility_common_test") {
  module_out_path = module_output_path
  sources = [
    "../src/accessibility_element_info.cpp",
    "../src/accessibility_event_info.cpp",
    "unittest/accessibility_ability_info_test.cpp",
    "unittest/accessibility_element_info_test.cpp",
    "unittest/accessibility_event_info_test.cpp",
    "unittest/accessibility_window_info_test.cpp",
  ]

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [ "../../../interfaces/innerkits/common:accessibility_common" ]

  external_deps = [
    "googletest:gmock_main",
    "googletest:gtest_main",
    "hilog:libhilog",
  ]
}

###############################################################################

group("unittest") {
  testonly = true
  deps = []

  deps += [ ":accessibility_common_test" ]
}
###############################################################################
