# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

ohos_shared_library("gesturepath_napi") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = false
    cfi_cross_dso = false
    debug = false
  }

  include_dirs = [
    "../../../../common/log/include",
    "../include",
    "include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_napi\"",
    "AAMS_LOG_DOMAIN = 0xD001D10",
  ]

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }

  sources = [
    "./src/napi_accessibility_gesture_path.cpp",
    "./src/native_module.cpp",
  ]

  configs = []

  deps = [ "../../../innerkits/common:accessibility_common" ]

  external_deps = [
    "hilog:libhilog",
    "napi:ace_napi",
  ]

  relative_install_dir = "module/accessibility"
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
