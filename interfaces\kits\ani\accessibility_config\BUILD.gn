# Copyright (C) 2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/config/components/ets_frontend/ets2abc_config.gni")
import("//build/ohos.gni")

ohos_shared_library("accessibility_config_ani") {
  include_dirs = [
    "../../../../common/log/include",
    "../../../../common/interface/include",
    "../../../../frameworks/acfwk/include",
    "./include",
  ]
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }
  deps = [
    "../../../innerkits/acfwk:accessibilityconfig",
    "../../../innerkits/common:accessibility_common",
  ]

  external_deps = [
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "hilog:libhilog",
    "input:libmmi-client",
    "runtime_core:ani",
    "runtime_core:ani_helpers",
  ]
  sources = [
    "../src/accessibility_utils_ani.cpp",
    "./src/ani_accessibility_config.cpp",
    "./src/ani_register.cpp",
  ]
  part_name = "accessibility"
  subsystem_name = "barrierfree"
  output_extension = "so"
}

generate_static_abc("accessibility_config_ani_abc") {
  base_url = "./ets"
  files = [ "./ets/@ohos.accessibility.config.ets" ]
  dst_file = "$target_out_dir/accessibility_config.abc"
  out_puts = [ "$target_out_dir/accessibility_config.abc" ]

  is_boot_abc = "True"
  device_dst_file = "/system/framework/accessibility_config.abc"
}

ohos_prebuilt_etc("accessibility_config_ani_abc_etc") {
  source = "$target_out_dir/accessibility_config.abc"
  module_install_dir = "framework"
  part_name = "accessibility"
  subsystem_name = "barrierfree"
  deps = [ ":accessibility_config_ani_abc" ]
}
