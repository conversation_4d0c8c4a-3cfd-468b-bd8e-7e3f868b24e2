# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")
import("../../../accessibility_manager_service.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
config("module_private_config") {
  visibility = [ ":*" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }

  include_dirs = [
    "../../../services/interface/include",
    "../include",
    "../../../interfaces/innerkits/acfwk/include",
    "../../../services/aams/test/mock/include",
    "../../../services/test/mock",
    "../../../common/log/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_test\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]
}

ohos_unittest("ac_unit_test") {
  module_out_path = module_output_path
  sources = [
    "../../../services/test/mock/mock_accessible_ability_manager_service_stub.cpp",
    "../../../services/test/mock/mock_parameter.c",
    "../src/accessibility_config.cpp",
    "../src/accessibility_config_impl.cpp",
    "mock/src/mock_service_registry.cpp",
    "unittest/accessibility_config_impl_test.cpp",
  ]

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:zuri",
    "ability_runtime:abilitykit_native",
    "ability_runtime:dataobs_manager",
    "ability_runtime:extension_manager",
    "bundle_framework:appexecfwk_base",
    "bundle_framework:appexecfwk_core",
    "c_utils:utils",
    "data_share:datashare_common",
    "data_share:datashare_consumer",
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "googletest:gmock_main",
    "googletest:gtest_main",
    "hilog:libhilog",
    "init:libbegetutil",
    "ipc:ipc_core",
    "power_manager:powermgr_client",
    "samgr:dynamic_cache",
    "samgr:samgr_proxy",
  ]

  if (accessibility_feature_display_manager) {
    external_deps += [ "display_manager:displaymgr" ]
  }
}

###############################################################################

group("unittest") {
  testonly = true
  deps = []

  deps += [ ":ac_unit_test" ]
}
###############################################################################
