{
    global:
	    extern "C++" {
            OHOS::BrokerDelegator*;
            OHOS::Accessibility::AccessibleAbilityManagerCaptionObserverProxy*;
            OHOS::Accessibility::AccessibilityElementOperatorCallbackProxy*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityManagerCaptionObserverProxy*;
            virtual?thunk?to?OHOS::Accessibility::AccessibilityElementOperatorCallbackStub*;
            OHOS::IRemoteProxy*;
            OHOS::IRemoteStub*;
            OHOS::Accessibility::AccessibilityElementOperatorCallbackStub*;
            non-virtual?thunk?to?OHOS::IRemoteStub*;
            OHOS::Accessibility::AccessibilityElementOperatorProxy*;
            OHOS::Accessibility::AccessibilityElementOperatorStub*;
            OHOS::Accessibility::AccessibilityEnableAbilityListsObserverProxy*;
            OHOS::Accessibility::AccessibilityEnableAbilityListsObserverStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibilityEnableAbilityListsObserverProxy*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityManagerConfigObserverProxy*;
            OHOS::Accessibility::AccessibleAbilityChannelProxy*;
            OHOS::Accessibility::AccessibleAbilityChannelStub*;
            OHOS::Accessibility::AccessibleAbilityClientProxy*;
          	OHOS::Accessibility::AccessibleAbilityClientStub*;
            OHOS::Accessibility::AccessibleAbilityManagerCaptionObserverStub*;
            OHOS::Accessibility::AccessibleAbilityManagerServiceProxy*;
            OHOS::Accessibility::AccessibleAbilityManagerServiceStub*;
            OHOS::Accessibility::AccessibleAbilityManagerStateObserverProxy*;
            OHOS::Accessibility::AccessibleAbilityManagerStateObserverStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityManagerStateObserverProxy*;
            OHOS::Accessibility::AccessibleAbilityManagerConfigObserverProxy*;
            OHOS::Accessibility::AccessibleAbilityManagerConfigObserverStub*;
            OHOS::Accessibility::AccessibilityAbilityInfoParcel*;
            OHOS::Accessibility::CaptionPropertyParcel*;
            OHOS::Accessibility::AccessibilityElementInfoParcel*;
            OHOS::Accessibility::AccessibleActionParcel*;
            OHOS::Accessibility::RectParcel*;
            OHOS::Accessibility::RangeInfoParcel*;
            OHOS::Accessibility::GridInfoParcel*;
            OHOS::Accessibility::GridItemInfoParcel*;
            OHOS::Accessibility::AccessibilityEventInfoParcel*;
            OHOS::Accessibility::AccessibilityWindowInfoParcel*;
            OHOS::Accessibility::AccessibilityGestureInjectPathParcel*;
            non-virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityChannel*;
            non-virtual?thunk?to?OHOS::Accessibility::RectParcel*;
            non-virtual?thunk?to?OHOS::Accessibility::AccessibilityElementOperatorCallbackStub*;
            non-virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityClientStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityClientStub*";
            non-virtual?thunk?to?OHOS::Accessibility::AccessibilityElementOperatorStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityClientStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibilityElementOperatorStub*;
            non-virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityManagerServiceStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityManagerServiceStub*;
            virtual?thunk?to?OHOS::Accessibility::AccessibleAbilityChannelStub*;
            OHOS::SystemAbilityLoadCallbackStub*;
            VTT?for?OHOS::SystemAbilityLoadCallbackStub*;
            VTT?for?OHOS::Accessibility::*Parcel;
            VTT?for?OHOS::Accessibility::*Proxy;
            VTT?for?OHOS::Accessibility::*Stub;
            vtable?for?OHOS::Accessibility::Rect*;
            vtable?for?OHOS::Accessibility::*Parcel*;
            vtable?for?OHOS::Accessibility::*Proxy*;
            vtable?for?OHOS::Accessibility::*Stub*;
            vtable?for?OHOS::BrokerDelegator*;
	    };
    local:
        *;
};