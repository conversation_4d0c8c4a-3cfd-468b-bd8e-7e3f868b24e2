# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#####################hydra-fuzz###################
import("//build/config/features.gni")
import("//build/test.gni")

##############################fuzztest##########################################
ohos_fuzztest("AccessibleAbilityManagerCaptionObserverStubFuzzTest") {
  module_out_path = "accessibility/accessibility"
  fuzz_config_file =
      "../../fuzztest/accessibleabilitymanagercaptionobserverstub_fuzzer"

  include_dirs = [ "../../../../../common/interface/include" ]
  configs = [
    "../../fuzztest:configs_cc_ld",
    "../../../../../resources/config/build:coverage_flags",
  ]
  sources = [ "accessibleabilitymanagercaptionobserverstub_fuzzer.cpp" ]
  deps = [
    "../../../../../interfaces/innerkits/common:accessibility_common",
    "../../../../interface:accessibility_interface",
  ]
  external_deps = [
    "c_utils:utils",
    "input:libmmi-client",
    "ipc:ipc_single",
  ]
}

###############################################################################
group("fuzztest") {
  testonly = true
  deps = []
  deps += [
    # deps file
    ":AccessibleAbilityManagerCaptionObserverStubFuzzTest",
  ]
}
###############################################################################
