# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//build/templates/abc/ohos_abc.gni")

es2abc_gen_abc("gen_accessibility_extension_abc") {
  src_js = rebase_path("accessibility_extension.js")
  dst_file = rebase_path(target_out_dir + "/accessibility_extension.abc")
  in_puts = [ "accessibility_extension.js" ]
  out_puts = [ target_out_dir + "/accessibility_extension.abc" ]
  extra_args = [ "--module" ]
}

gen_js_obj("accessibility_extension_js") {
  input = "accessibility_extension.js"
  output = target_out_dir + "/accessibility_extension.o"
}

gen_js_obj("accessibility_extension_abc") {
  input = get_label_info(":gen_accessibility_extension_abc", "target_out_dir") +
          "/accessibility_extension.abc"
  output = target_out_dir + "/accessibility_extension_abc.o"
  dep = ":gen_accessibility_extension_abc"
}

ohos_shared_library("accessibilityextensionability_napi") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = false
    cfi_cross_dso = false
    debug = false
  }

  sources = [ "accessibility_extension_module.cpp" ]

  deps = [
    ":accessibility_extension_abc",
    ":accessibility_extension_js",
  ]

  external_deps = [ "napi:ace_napi" ]

  relative_install_dir = "module/application"
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
