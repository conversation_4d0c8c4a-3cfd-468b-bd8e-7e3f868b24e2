# Copyright (c) 2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

group("api_event_etc") {
  deps = [ ":api_event_reporter_etc" ]
}

ohos_prebuilt_etc("api_event_reporter_etc") {
  source = "api_event_reporter.cfg"
  relative_install_dir = "accessibility"
  part_name = "accessibility"
  subsystem_name = "barrierfree"
}
