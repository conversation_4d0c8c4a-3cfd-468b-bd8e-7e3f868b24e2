/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package OHOS.Accessibility;
 
struct RegistrationPara {
    int windowId;
    int parentWindowId;
    int parentTreeId;
    long elementId;
};
 
struct AccessibilityConfigData {
    boolean highContrastText_;
    boolean invertColor_;
    boolean animationOff_;
    boolean audioMono_;
    boolean mouseKey_;
    boolean captionState_;
    boolean screenMagnifier_;
    boolean shortkey_;
    int mouseAutoClick_;
    boolean daltonizationState_;
    unsigned int daltonizationColorFilter_;
    unsigned int contentTimeout_;
    float brightnessDiscount_;
    float audioBalance_;
    String shortkeyTarget_;
    unsigned int clickResponseTime_;
    boolean ignoreRepeatClickState_;
    unsigned int ignoreRepeatClickTime_;
    String[] shortkeyMultiTarget_;
};