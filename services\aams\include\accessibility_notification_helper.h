/*
 * Copyright (C) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed On an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
#ifndef NOTIFICATION_HELPER_H
#define NOTIFICATION_HELPER_H
 
#include "accessibility_def.h"
 
namespace OHOS {
namespace Accessibility {
class IgnoreRepeatClickNotification {
public:
    static int32_t PublishIgnoreRepeatClickReminder();
    static void CancelNotification();
    static int32_t RegisterTimers(uint64_t beginTime);
    static void DestoryTimers();
    static int64_t GetWallTimeMs();
private:
    static bool IsSendIgnoreRepeatClickNotification();
};
} // namespace Accessibility
} // namespace OHOS
 
#endif // NOTIFICATION_HELPER_H