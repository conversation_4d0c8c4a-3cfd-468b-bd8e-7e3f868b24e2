# Copyright (c) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")

ohos_benchmarktest("BenchmarkTestForAccessibilityConfig") {
  module_out_path = "accessibility/acfwk"
  sources = [ "accessibility_config_test.cpp" ]

  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }

  deps = [
    "../../../acfwk:accessibilityconfig",
    "../../../common:accessibility_common",
  ]

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libnativetoken",
    "access_token:libtoken_setproc",
    "cJSON:cjson_static",
    "selinux_adapter:librestorecon",
    "c_utils:utils",
    "ffrt:libffrt",
  ]
}

group("benchmarktest") {
  testonly = true
  deps = []

  deps += [
    # deps file
    ":BenchmarkTestForAccessibilityConfig",
  ]
}
