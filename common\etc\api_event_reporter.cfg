# Copyright (c) 2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

{
    "api_operation_management": {
        "report_config": {
            "config_name": "accessibility_processor",
            "config_appId": "accessibility_ohos_sdk_ocg",
            "config_routeInfo": "AUTO",
            "config_TriggerCond.timeout": "90",
            "config_TriggerCond.row": "30"
        },
        "event_config": {
            "event1": {
                "domain": "api_diagnostic",
                "name": "api_exec_end",
                "isRealTime": "false"
            },
            "event2": {
                "domain": "api_diagnostic",
                "name": "api_called_stat",
                "isRealTime": "true"
            },
            "event3": {
                "domain": "api_diagnostic",
                "name": "api_called_stat_cnt",
                "isRealTime": "true"
            }
        }
    }
}