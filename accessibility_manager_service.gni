# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

declare_args() {
  accessibility_feature_power_manager = true
  accessibility_feature_display_manager = true
  accessibility_feature_data_share = true
  accessibility_use_rosen_drawing = false
  accessibility_watch_feature = false
  accessibility_feature_hiviewdfx_hitrace = true
  accessibility_feature_hiviewdfx_hisysevent = true
  accessibility_dynamic_support = false
  security_component_enable = false
  if (defined(global_parts_info) &&
      !defined(global_parts_info.powermgr_display_manager)) {
    accessibility_feature_display_manager = false
  }

  if (defined(global_parts_info) &&
      !defined(global_parts_info.powermgr_power_manager)) {
    accessibility_feature_power_manager = false
  }

  if (defined(use_rosen_drawing) && use_rosen_drawing) {
    accessibility_use_rosen_drawing = true
  }

  if (defined(global_parts_info) &&
      !defined(global_parts_info.distributeddatamgr_data_share)) {
    distributeddatamgr_data_share = false
  }
  if (defined(global_parts_info) &&
      !defined(global_parts_info.hiviewdfx_hitrace)) {
    accessibility_feature_hiviewdfx_hitrace = false
  }

  if (defined(global_parts_info) &&
      !defined(global_parts_info.hiviewdfx_hisysevent)) {
    accessibility_feature_hiviewdfx_hisysevent = false
  }

  if (!defined(global_parts_info) ||
      defined(global_parts_info.security_security_component_manager)) {
    security_component_enable = true
  }
}

print(
    "accessibility_feature_display_manager = ${accessibility_feature_display_manager}")
print(
    "accessibility_feature_power_manager = ${accessibility_feature_power_manager}")
print("accessibility_use_rosen_drawing = ${accessibility_use_rosen_drawing}")
print("accessibility_feature_data_share = ${accessibility_feature_data_share}")
print(
    "accessibility_feature_hiviewdfx_hitrace = ${accessibility_feature_hiviewdfx_hitrace}")
print(
    "accessibility_feature_hiviewdfx_hisysevent = ${accessibility_feature_hiviewdfx_hisysevent}")

accessibility_default_defines = []

if (accessibility_feature_power_manager) {
  accessibility_default_defines += [ "OHOS_BUILD_ENABLE_POWER_MANAGER" ]
}

if (accessibility_feature_display_manager) {
  accessibility_default_defines += [ "OHOS_BUILD_ENABLE_DISPLAY_MANAGER" ]
}

if (accessibility_use_rosen_drawing) {
  accessibility_default_defines += [ "USE_ROSEN_DRAWING" ]
}

if (accessibility_feature_data_share) {
  accessibility_default_defines += [ "OHOS_BUILD_ENABLE_DATA_SHARE" ]
}

if (defined(accessibility_watch_feature) && accessibility_watch_feature) {
  accessibility_default_defines += [ "ACCESSIBILITY_WATCH_FEATURE" ]
}

if (accessibility_feature_hiviewdfx_hitrace) {
  accessibility_default_defines += [ "OHOS_BUILD_ENABLE_HITRACE" ]
}

if (accessibility_feature_hiviewdfx_hisysevent) {
  accessibility_default_defines += [ "OHOS_BUILD_ENABLE_HISYSEVENT" ]
}

if (product_name != "qemu-arm-linux-min") {
  accessibility_default_defines += [ "ACCESSIBILITY_EMULATOR_DEFINED" ]
}

if (security_component_enable) {
  accessibility_default_defines += [ "ACCESSIBILITY_SECURITY_COMPONENT" ]
}