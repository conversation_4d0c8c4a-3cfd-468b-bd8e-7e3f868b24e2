# Copyright (C) 2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("../../accessibility_manager_service.gni")

services_ext_path = "./"

config("aams_ext_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "include",
    "../../common/log/include",
    "../aams/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility\"",
    "AAMS_LOG_DOMAIN = 0xD001D00",
  ]

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }

  defines += accessibility_default_defines
}

aams_ext_files = [
  "${services_ext_path}/src/magnification_window.cpp",
  "${services_ext_path}/src/magnification_menu.cpp",
  "${services_ext_path}/src/ext_utils.cpp",
  "${services_ext_path}/src/accessibility_notification_helper.cpp",
  "${services_ext_path}/src/export_api.cpp",
]

ohos_shared_library("aams_ext") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }

  sources = aams_ext_files

  configs = [
    ":aams_ext_config",
    "../../resources/config/build:coverage_flags",
  ]

  external_deps = [
    "ability_base:zuri",
    "ability_runtime:wantagent_innerkits",
    "c_utils:utils",
    "graphic_2d:2d_graphics",
    "graphic_2d:librender_service_client",
    "graphic_2d:librender_service_base",
    "hisysevent:libhisysevent",
    "i18n:intl_util",
    "os_account:os_account_innerkits",
    "resource_management:global_resmgr",
    "time_service:time_client",
    "window_manager:libdm",
    "window_manager:libwm",
  ]

  if (defined(global_parts_info) &&
    defined(
      global_parts_info.notification_distributed_notification_service)) {
    external_deps += [ "distributed_notification_service:ans_innerkits" ]
  }

  install_enable = true

  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
