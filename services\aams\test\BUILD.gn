# Copyright (C) 2022-2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")
import("../../../accessibility_manager_service.gni")
import("../../test/aamstestmock.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
#1. intent(c++) get/set test without transport
config("module_private_config") {
  visibility = [ ":*" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }

  include_dirs = aams_mock_include_dirs
  include_dirs += [
    "../../../common/log/include",
    "../../../interfaces/innerkits/acfwk/include",
    "../../../interfaces/innerkits/asacfwk/include",
    "../../../interfaces/innerkits/common/include",
    "../include",
    "./mock/include",
    "../../test/mock/common",
    "../../../common/interface/include",
    "../../../common/interface/include/parcel",
    "mock",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_test\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]

  defines += accessibility_default_defines
}

test_external_deps = [
  "ability_base:want",
  "ability_base:zuri",
  "ability_runtime:ability_manager",
  "ability_runtime:abilitykit_native",
  "ability_runtime:app_manager",
  "ability_runtime:dataobs_manager",
  "ability_runtime:extension_manager",
  "access_token:libaccesstoken_sdk",
  "access_token:libnativetoken",
  "access_token:libtoken_setproc",
  "access_token:libtokenid_sdk",
  "bundle_framework:appexecfwk_core",
  "c_utils:utils",
  "cJSON:cjson_static",
  "common_event_service:cesfwk_innerkits",
  "data_share:datashare_consumer",
  "eventhandler:libeventhandler",
  "ffrt:libffrt",
  "googletest:gmock_main",
  "googletest:gtest_main",
  "graphic_2d:2d_graphics",
  "graphic_2d:librender_service_client",
  "graphic_2d:librender_service_base",
  "hicollie:libhicollie",
  "hilog:libhilog",
  "hisysevent:libhisysevent",
  "hitrace:hitrace_meter",
  "i18n:intl_util",
  "init:libbeget_proxy",
  "init:libbegetutil",
  "input:libmmi-client",
  "ipc:ipc_single",
  "memmgr:memmgrclient",
  "os_account:os_account_innerkits",
  "power_manager:powermgr_client",
  "preferences:native_preferences",
  "safwk:system_ability_fwk",
  "samgr:samgr_proxy",
  "selinux_adapter:librestorecon",
  "window_manager:libdm",
  "window_manager:libwm_lite",
]

if (accessibility_feature_display_manager) {
  test_external_deps += [ "display_manager:displaymgr" ]
}
if (security_component_enable) {
  test_external_deps += [ "security_component_manager:libsecurity_component_sdk" ]
}

################################################################################
ohos_unittest("accessibility_ability_manager_test") {
  module_out_path = module_output_path

  sources = [
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_settings_config.cpp",
    "mock/src/mock_accessibility_setting_provider.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_proxy.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_ability_manager_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("element_operator_manager_test") {
  module_out_path = module_output_path

  sources = [
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/accessibility_settings_config.cpp",
    "mock/src/mock_accessibility_setting_provider.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_proxy.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/element_operator_manager_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_account_data_test") {
  module_out_path = module_output_path

  sources = [
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_accessibility_setting_provider.cpp",
    "mock/src/mock_accessibility_settings_config.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_proxy.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_account_data_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessible_ability_manager_service_test") {
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_bundle_mgr_proxy.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../../test/mock/mock_parameter.c",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/aafwk/mock_bundle_manager.cpp",
    "mock/src/mock_accessibility_element_operator_callback_stub.cpp",
    "mock/src/mock_accessibility_short_key_dialog.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_proxy.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessible_ability_manager_service_test.cpp",
  ]

  if (accessibility_feature_display_manager) {
    sources += [ "mock/src/mock_display_power_mgr_client.cpp" ]
  }

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_touch_exploration_test") {
  module_out_path = module_output_path
  sources = [
    "../../../common/interface/src/accessibility_element_operator_callback_stub.cpp",
    "../../../common/interface/src/accessible_ability_channel_stub.cpp",
    "../../../common/interface/src/parcel/accessibility_element_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_event_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_gesture_inject_path_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_window_info_parcel.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_display_manager.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_element_operator_proxy.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_channel_proxy.cpp",
    "mock/src/mock_accessible_ability_client_proxy.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_accessible_ability_manager_service_stub.cpp",
    "mock/src/mock_display.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_touch_exploration_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_common_event_registry_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../../test/mock/mock_os_account_manager.cpp",
    "../../test/mock/mock_parameter.c",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_common_event_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_input_interceptor_test") {
  module_out_path = module_output_path

  sources = [
    "../../../common/interface/src/accessible_ability_channel_proxy.cpp",
    "../../../common/interface/src/accessible_ability_client_proxy.cpp",
    "../../../frameworks/common/src/accessibility_ability_info.cpp",
    "../../../frameworks/common/src/accessibility_caption.cpp",
    "../../../frameworks/common/src/accessibility_event_info.cpp",
    "../../../frameworks/common/src/accessibility_window_info.cpp",
    "../../test/mock/mock_os_account_manager.cpp",
    "../../test/mock/mock_parameter.c",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/aafwk/mock_bundle_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessibility_keyevent_filter.cpp",
    "mock/src/mock_accessibility_touchEvent_injector.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_accessible_ability_manager_service_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_input_interceptor_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src
  sources += aams_mock_multimodalinput_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_window_manager_test") {
  module_out_path = module_output_path

  sources = [
    "../../../common/interface/src/accessibility_element_operator_callback_proxy.cpp",
    "../../../common/interface/src/accessibility_element_operator_proxy.cpp",
    "../../../common/interface/src/accessible_ability_channel_proxy.cpp",
    "../../../common/interface/src/accessible_ability_client_proxy.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/aafwk/mock_bundle_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_rosen_window_info.cpp",
    "mock/src/mock_system_ability.cpp",
    "mock/src/mock_window_manager.cpp",
    "unittest/accessibility_window_manager_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_keyevent_filter_test") {
  module_out_path = module_output_path
  sources = [
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "./mock/src/mock_accessibility_account_data.cpp",
    "./mock/src/mock_accessibility_event_transmission.cpp",
    "./mock/src/mock_accessible_ability_connection.cpp",
    "./mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_keyevent_filter_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessible_ability_connection_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_ability_manager_client.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../../test/mock/mock_os_account_manager.cpp",
    "../../test/mock/mock_parameter.c",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/aafwk/mock_bundle_manager.cpp",
    "mock/src/mock_accessibility_element_operator_callback_stub.cpp",
    "mock/src/mock_accessibility_element_operator_stub.cpp",
    "mock/src/mock_accessibility_keyevent_filter.cpp",
    "mock/src/mock_accessibility_setting_provider.cpp",
    "mock/src/mock_accessibility_short_key_dialog.cpp",
    "mock/src/mock_accessible_ability_channel_proxy.cpp",
    "mock/src/mock_accessible_ability_client_proxy.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service_state_observer_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessible_ability_connection_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_display_manager_test") {
  module_out_path = module_output_path
  sources = [
    "../../../frameworks/common/src/accessibility_constants.cpp",
    "../../../frameworks/common/src/accessibility_event_info.cpp",
    "../../../frameworks/common/src/accessibility_window_info.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/utils.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "unittest/accessibility_display_manager_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [ "../../../common/interface:accessibility_interface" ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_dumper_test") {
  module_out_path = module_output_path
  sources = [
    "../../../common/interface/src/accessible_ability_channel_stub.cpp",
    "../../../common/interface/src/parcel/accessibility_element_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_gesture_inject_path_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_window_info_parcel.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/utils.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessibility_settings_config.cpp",
    "mock/src/mock_accessibility_window_connection.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_channel_proxy.cpp",
    "mock/src/mock_accessible_ability_client_proxy.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_accessible_ability_manager_service_stub.cpp",
    "mock/src/mock_display.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_dumper_test.cpp",
  ]
  sources += aams_mock_multimodalinput_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_touchevent_injector_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_touchEvent_injector_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessible_ability_channel_test") {
  module_out_path = module_output_path
  sources = [
    "../../../common/interface/src/accessibility_element_operator_callback_proxy.cpp",
    "../../../common/interface/src/accessible_ability_channel_stub.cpp",
    "../../../common/interface/src/parcel/accessibility_element_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_gesture_inject_path_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_window_info_parcel.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_display_manager.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessible_ability_channel.cpp",                                                                                                                                                                                             
    "../src/accessible_ability_connection.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/utils.cpp",
    "mock/src/mock_accessibility_element_operator_proxy.cpp",
    "mock/src/mock_accessibility_element_operator_stub.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessibility_keyevent_filter.cpp",
    "mock/src/mock_accessibility_setting_provider.cpp",
    "mock/src/mock_accessibility_settings_config.cpp",
    "mock/src/mock_accessibility_touchEvent_injector.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_client_proxy.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_accessible_ability_manager_service_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessible_ability_channel_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_mouse_autoclick_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_settings_config.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_mouse_autoclick_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_screen_touch_test") {
  module_out_path = module_output_path
  sources = [
    "../../../common/interface/src/accessibility_element_operator_callback_stub.cpp",
    "../../../common/interface/src/accessible_ability_channel_stub.cpp",
    "../../../common/interface/src/parcel/accessibility_element_info_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_gesture_inject_path_parcel.cpp",
    "../../../common/interface/src/parcel/accessibility_window_info_parcel.cpp",
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "mock/src/mock_accessibility_account_data.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessibility_settings_config.cpp",
    "mock/src/mock_accessibility_window_manager.cpp",
    "mock/src/mock_accessible_ability_channel_proxy.cpp",
    "mock/src/mock_accessible_ability_client_proxy.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_connection.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_accessible_ability_manager_service_stub.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_screen_touch_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_mouse_key_test") {
  module_out_path = module_output_path
  sources = [
    "../src/accessibility_mouse_key.cpp",
    "../src/utils.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "unittest/accessibility_mouse_key_test.cpp",
  ]

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common"
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_short_key_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_short_key_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_short_key_dialog_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/magnification_window_proxy.cpp",
    "../src/utils.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessibility_input_interceptor.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_short_key_dialog_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]
  
  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_settings_config_test") {
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }
  module_out_path = module_output_path
  sources = [
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_common_event.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_display_manager.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_event_transmission.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/msdp_manager.cpp",
    "../src/accessibility_security_component_manager.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/magnification_menu_manager.cpp",
    "../src/full_screen_magnification_manager.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_magnification_manager.cpp",
    "unittest/accessibility_settings_config_test.cpp",
  ]

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
ohos_unittest("accessibility_zoom_gesture_test") {
  module_out_path = module_output_path
  sources = [
    "../../test/mock/mock_common_event_data.cpp",
    "../../test/mock/mock_common_event_manager.cpp",
    "../../test/mock/mock_common_event_subscribe_info.cpp",
    "../../test/mock/mock_common_event_subscriber.cpp",
    "../../test/mock/mock_matching_skill.cpp",
    "../src/accessibility_account_data.cpp",
    "../src/accessibility_ability_manager.cpp",
    "../src/element_operator_manager.cpp",
    "../src/accessibility_circle_drawing_manager.cpp",
    "../src/accessibility_datashare_helper.cpp",
    "../src/accessibility_dumper.cpp",
    "../src/accessibility_input_interceptor.cpp",
    "../src/accessibility_keyevent_filter.cpp",
    "../src/accessibility_mouse_autoclick.cpp",
    "../src/accessibility_mouse_key.cpp",
    "../src/accessibility_power_manager.cpp",
    "../src/accessibility_resource_bundle_manager.cpp",
    "../src/accessibility_screen_touch.cpp",
    "../src/accessibility_setting_observer.cpp",
    "../src/accessibility_setting_provider.cpp",
    "../src/accessibility_settings.cpp",
    "../src/accessibility_settings_config.cpp",
    "../src/accessibility_notification_helper.cpp",
    "../src/accessibility_short_key.cpp",
    "../src/accessibility_short_key_dialog.cpp",
    "../src/accessibility_touchEvent_injector.cpp",
    "../src/accessibility_window_connection.cpp",
    "../src/accessibility_window_manager.cpp",
    "../src/accessibility_zoom_gesture.cpp",
    "../src/accessible_ability_channel.cpp",
    "../src/accessible_ability_connection.cpp",
    "../src/accessible_ability_manager_service_event_handler.cpp",
    "../src/touch_exploration_multi_finger_gesture.cpp",
    "../src/touch_exploration_single_finger_gesture.cpp",
    "../src/utils.cpp",
    "../src/magnification_manager.cpp",
    "../src/window_magnification_gesture.cpp",
    "../src/window_magnification_manager.cpp",
    "../src/magnification_window_proxy.cpp",
    "mock/src/mock_accessibility_common_event.cpp",
    "mock/src/mock_accessibility_display_manager.cpp",
    "mock/src/mock_accessibility_event_transmission.cpp",
    "mock/src/mock_accessible_ability_client_stub_impl.cpp",
    "mock/src/mock_accessible_ability_manager_service.cpp",
    "mock/src/mock_full_screen_magnification_manager.cpp",
    "mock/src/mock_magnification_menu_manager.cpp",
    "mock/src/mock_system_ability.cpp",
    "unittest/accessibility_zoom_gesture_test.cpp",
  ]
  sources += aams_mock_distributeddatamgr_src

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = test_external_deps
}

################################################################################
group("unittest") {
  testonly = true
  deps = []

  deps += [
    ":accessibility_ability_manager_test",
    ":accessibility_account_data_test",
    ":accessibility_common_event_registry_test",
    ":element_operator_manager_test",
    ":accessibility_display_manager_test",
    ":accessibility_dumper_test",
    ":accessibility_input_interceptor_test",
    ":accessibility_keyevent_filter_test",
    ":accessibility_mouse_autoclick_test",
    ":accessibility_mouse_key_test",
    ":accessibility_screen_touch_test",
    ":accessibility_settings_config_test",
    ":accessibility_short_key_test",
    ":accessibility_touch_exploration_test",
    ":accessibility_touchevent_injector_test",
    ":accessibility_window_manager_test",
    ":accessibility_zoom_gesture_test",
    ":accessible_ability_channel_test",
    ":accessible_ability_connection_test",
    ":accessible_ability_manager_service_test",
  ]
}