# Copyright (C) 2022-2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/config/components/idl_tool/idl.gni")
import("//build/ohos.gni")

idl_gen_interface("accessible_ability_manager_service_interface") {
  sources = [ "IAccessibleAbilityManagerService.idl" ]
  sources_common = [ "AccessibilityIpcTypes.idl" ]
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}

config("accessibility_interface_private_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "include/parcel",
    "../log/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_interface\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]

  if (target_cpu == "arm") {
    cflags = [ "-DBINDER_IPC_32BIT" ]
  }

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }
}

config("accessibility_interface_public_config") {
  include_dirs = [
    "${target_gen_dir}",
    "include",
  ]
}

accessibility_interface_src = [
  "src/accessibility_element_operator_callback_proxy.cpp",
  "src/accessibility_element_operator_callback_stub.cpp",
  "src/accessibility_element_operator_proxy.cpp",
  "src/accessibility_element_operator_stub.cpp",
  "src/accessibility_enable_ability_lists_observer_proxy.cpp",
  "src/accessibility_enable_ability_lists_observer_stub.cpp",
  "src/accessible_ability_channel_proxy.cpp",
  "src/accessible_ability_channel_stub.cpp",
  "src/accessible_ability_client_proxy.cpp",
  "src/accessible_ability_client_stub.cpp",
  "src/accessible_ability_manager_caption_observer_proxy.cpp",
  "src/accessible_ability_manager_caption_observer_stub.cpp",
  "src/accessible_ability_manager_config_observer_proxy.cpp",
  "src/accessible_ability_manager_config_observer_stub.cpp",
  "src/accessible_ability_manager_state_observer_proxy.cpp",
  "src/accessible_ability_manager_state_observer_stub.cpp",
]

accessibility_data_parcel_src = [
  "src/parcel/accessibility_ability_info_parcel.cpp",
  "src/parcel/accessibility_caption_parcel.cpp",
  "src/parcel/accessibility_element_info_parcel.cpp",
  "src/parcel/accessibility_event_info_parcel.cpp",
  "src/parcel/accessibility_gesture_inject_path_parcel.cpp",
  "src/parcel/accessibility_window_info_parcel.cpp",
]

ohos_shared_library("accessibility_interface") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }

  output_values =
      get_target_outputs(":accessible_ability_manager_service_interface")
  sources = accessibility_interface_src
  sources += accessibility_data_parcel_src
  sources += filter_include(output_values, [ "*proxy.cpp" ])
  sources += filter_include(output_values, [ "*stub.cpp" ])
  sources += filter_include(output_values, [ "*types.cpp" ])
  configs = [
    ":accessibility_interface_private_config",
    "../../resources/config/build:coverage_flags",
  ]

  public_configs = [ ":accessibility_interface_public_config" ]

  deps = [
    ":accessible_ability_manager_service_interface",
    "../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "c_utils:utils",
    "ffrt:libffrt",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_single",
  ]

  install_enable = true

  subsystem_name = "barrierfree"
  innerapi_tags = [ "platformsdk_indirect" ]
  part_name = "accessibility"
}
