{
    global:
        extern "C++" {
            OHOS::Accessibility::AccessibilityAbilityInfo*;
            OHOS::AccessibilityConfig::CaptionProperty*;
            OHOS::Accessibility::AccessibilityElementInfo*;
            OHOS::Accessibility::AccessibleAction*;
            OHOS::Accessibility::RangeInfo*;
            OHOS::Accessibility::GridInfo*;
            OHOS::Accessibility::GridItemInfo*;
            OHOS::Accessibility::AccessibilityMemo*;
            OHOS::Accessibility::AccessibilityEventInfo*;
            OHOS::Accessibility::AccessibilityWindowInfo*;
            OHOS::Accessibility::AccessibilityGestureInjectPath*;
            OHOS::Accessibility::OHOS_PERMISSION*;
            OHOS::Accessibility::STATE_*;
            OHOS::Accessibility::ANY_WINDOW_ID;
            OHOS::Accessibility::FOCUS_*;
            OHOS::Accessibility::INVALID_*;
            OHOS::Accessibility::ROOT_*;
            OHOS::Accessibility::GET_SOURCE_PREFETCH_MODE;
            OHOS::Accessibility::ACTIVE_WINDOW_ID;
            OHOS::Accessibility::ACTION_ARGU_*;
            OHOS::Accessibility::PREFETCH_*;
            OHOS::Accessibility::HTML_ITEM_*;
            OHOS::Accessibility::HOST_VIEW_ID;
            OHOS::Accessibility::MAX_TEXT_LENGTH;
            OHOS::Accessibility::MOVE_UNIT_*;
            OHOS::Accessibility::UNDEFINED*;
            OHOS::Accessibility::SELECTION_*;
            OHOS::AccessibilityNapi::Convert*;
            OHOS::AccessibilityNapi::Parse*;
            OHOS::AccessibilityNapi::Check*;
            OHOS::AccessibilityNapi::Get*;
            OHOS::AccessibilityNapi::Set*;
            OHOS::AccessibilityNapi::HasKeyCode*;
            OHOS::AccessibilityNapi::Query*;
            OHOS::AccessibilityNapi::Create*;
            OHOS::AccessibilityNapi::Transform*;
            vtable?for?OHOS::Accessibility::Rect*;
    };
    local:
        *;
};