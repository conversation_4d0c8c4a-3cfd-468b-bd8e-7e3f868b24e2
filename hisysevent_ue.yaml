#  Copyright (c) 2024 Huawei Device Co., Ltd.
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

#####################################################
#     below is the format of defining event         #
#####################################################
#domain: domain name.  [Only one domain name can be defined at the top]
#
#author: the author name who defined this event.
#date: the date when this event was defined, format is YYYY-MM-DD.
#logged: source file which refer to this event.
#usage: the usage of this event.
#//Define event name and event properties.
#@EVENT_NAME: the event definition part begin.
#  // __BASE is used for defining the basic info of the event.
#  // "type" optional values are: FAULT, STATISTICS, SECURITY, BEHAVIOR.
#  // "level" optional values are: CRITICAL, MINOR.
#  // "tag" set tags with may used by subscriber of this event, multiple tags divided by space.
#  // "desc" full description of this event.
#  @PARAMETER: {type: parameter type, arrsize: array length(optional), desc: parameter description}.
#  // follow the __BASE block, each line defines a parameter of this event.
#  // "type" optional values are: INT8, UINT8, INT16, UINT16, INT32, UINT32, INT64, UINT64, FLOAT, DOUBLE, STRING.
#  // "arrsize" of the parameter is an array, set a non-zero value.
#  // "desc" full description of this parameter.

#####################################################
#   Example of some hiviewdfx events definition     #
#####################################################

domain: ACCESSIBILITY_UE

ENABLE_SHORTKEY_ABILITY_SINGLE:
  __BASE: {type: BEHAVIOR, level: CRITICAL, desc: enable single ability targets}
  PNAMEID: {type: STRING, desc: package name}
  PVERSIONID: {type: STRING, desc: application version}
  MSG_NAME: {type: STRING, desc: enable single targets name}
  MSG_VALUE: {type: STRING, desc: enable single targets value}

ZOOM_GESTURE_ACTION:
  __BASE: {type: BEHAVIOR, level: CRITICAL, desc: on zoom gesture state}
  PNAMEID: {type: STRING, desc: package name}
  PVERSIONID: {type: STRING, desc: application version}
  MSG_NAME: {type: STRING, desc: on zoom gesture state name}
  MSG_VALUE: {type: STRING, desc: on zoom gesture state value}