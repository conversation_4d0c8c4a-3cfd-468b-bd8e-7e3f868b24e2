# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

accessibility_common_path = "../../../frameworks/common"

config("accessibility_common_private_config") {
  visibility = [ ":*" ]

  include_dirs = [ "../../../common/log/include" ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_common\"",
    "AAMS_LOG_DOMAIN = 0xD001D04",
  ]

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }
}

config("accessibility_common_public_config") {
  include_dirs = [ "include" ]
}

accessibility_common_src = [
  "${accessibility_common_path}/src/accessibility_ability_info.cpp",
  "${accessibility_common_path}/src/accessibility_caption.cpp",
  "${accessibility_common_path}/src/accessibility_constants.cpp",
  "${accessibility_common_path}/src/accessibility_element_info.cpp",
  "${accessibility_common_path}/src/accessibility_event_info.cpp",
  "${accessibility_common_path}/src/accessibility_permission.cpp",
  "${accessibility_common_path}/src/accessibility_window_info.cpp",
  "${accessibility_common_path}/src/accessibility_gesture_inject_path.cpp",
]

ohos_shared_library("accessibility_common") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }

  sources = accessibility_common_src
  configs = [
    ":accessibility_common_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  public_configs = [ ":accessibility_common_public_config" ]

  install_enable = true

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "c_utils:utils",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_single",
    "runtime_core:ani",
  ]
  innerapi_tags = [ "platformsdk" ]
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
