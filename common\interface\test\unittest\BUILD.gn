# Copyright (C) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
config("module_parcel_private_config") {
  visibility = [ ":*" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }
  include_dirs = [
    "../../include/parcel",
    "../../../../common/log/include",
    "../../../../interfaces/innerkits/common/include",
  ]
  defines = [
    "AAMS_LOG_TAG = \"accessibility_parcel\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]
}

accessibility_parcel_src = [
  "../../src/parcel/accessibility_ability_info_parcel.cpp",
  "../../src/parcel/accessibility_caption_parcel.cpp",
  "../../src/parcel/accessibility_element_info_parcel.cpp",
  "../../src/parcel/accessibility_event_info_parcel.cpp",
  "../../src/parcel/accessibility_window_info_parcel.cpp",
]

accessibility_parcel_test_src = [
  "accessibility_ability_info_parcel_test.cpp",
  "accessibility_caption_parcel_test.cpp",
  "accessibility_element_info_parcel_test.cpp",
  "accessibility_event_info_parcel_test.cpp",
  "accessibility_window_info_parcel_test.cpp",
]

ohos_unittest("accessibility_parcel_test") {
  module_out_path = module_output_path

  sources = accessibility_parcel_src
  sources += accessibility_parcel_test_src

  configs = [
    ":module_parcel_private_config",
    "../../../../resources/config/build:coverage_flags",
  ]

  deps = [ "../../../../interfaces/innerkits/common:accessibility_common" ]

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "c_utils:utils",
    "googletest:gmock_main",
    "googletest:gtest_main",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_single",
  ]
}

###############################################################################

group("unittest") {
  testonly = true
  deps = []

  deps += [ ":accessibility_parcel_test" ]
}
###############################################################################
