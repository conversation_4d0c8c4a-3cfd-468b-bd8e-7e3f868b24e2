/*
 * Copyright (C) 2021 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "accessibility_account_data.h"
#include "accessible_ability_manager_service.h"
#include "element_operator_manager.h"
#include "hilog_wrapper.h"
#include "utils.h"

namespace OHOS {
namespace Accessibility {
namespace {
    const std::string PREF_TEST_PATH = "/data/service/el1/public/barrierfree/accessibility_ability_manager_service/";
} // namespace

MockAccessibilityAccountData::AccessibilityAccountData(int32_t accountId)
{
    id_ = accountId;
    {
        std::lock_guard<ffrt::mutex> lock(abilityManagerMutex_);
        abilityManagerMap_[id_] = std::make_shared<AccessibilityAbilityManager>(id_);
    }
}

MockAccessibilityAccountData::~AccessibilityAccountData()
{}

int32_t MockAccessibilityAccountData::GetAccountId()
{
    HILOG_DEBUG("start.");
    return id_;
}

// get client state.
uint32_t MockAccessibilityAccountData::GetAccessibilityState()
{
    return 0;
}

// switch the user causes state changed.
void MockAccessibilityAccountData::OnAccountSwitched()
{
    HILOG_DEBUG("start.");
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->OnAccountSwitched();
    }

    // Clear Setting State.
    isEventTouchGuideState_ = false;
    isScreenMagnification_ = false;
    isFilteringKeyEvents_ = false;
    isGesturesSimulation_ = false;
}

// add connect ability.
void MockAccessibilityAccountData::AddConnectedAbility(sptr<AccessibleAbilityConnection>& connection)
{
    if (!connection) {
        HILOG_ERROR("connection is nullptr");
        return;
    }
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->AddConnectedAbility(connection);
    }
}

// remove connect ability.
void MockAccessibilityAccountData::RemoveConnectedAbility(const AppExecFwk::ElementName &element)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->RemoveConnectedAbility(element);
    }
}

void MockAccessibilityAccountData::AddCaptionPropertyCallback(
    const sptr<IAccessibleAbilityManagerCaptionObserver>& callback)
{
    (void)callback;
}

void MockAccessibilityAccountData::RemoveCaptionPropertyCallback(const wptr<IRemoteObject>& callback)
{
    (void)callback;
    HILOG_DEBUG("start.");
}
// add AccessibilityInteractionConnection
void MockAccessibilityAccountData::AddAccessibilityWindowConnection(
    const int32_t windowId, const sptr<AccessibilityWindowConnection>& interactionConnection)
{
    HILOG_DEBUG("Mock: AddAccessibilityWindowConnection windowId(%{public}d)", windowId);
    operatorManager_.AddAccessibilityWindowConnection(windowId, interactionConnection);
}

// remove AccessibilityWindowConnection
void MockAccessibilityAccountData::RemoveAccessibilityWindowConnection(const int32_t windowId)
{
    HILOG_DEBUG("Mock: RemoveAccessibilityWindowConnection windowId(%{public}d)", windowId);
    operatorManager_.RemoveAccessibilityWindowConnection(windowId);
}

void MockAccessibilityAccountData::AddConnectingA11yAbility(const std::string &uri,
    const sptr<AccessibleAbilityConnection> &connection)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->AddConnectingA11yAbility(uri, connection);
    }
}

void MockAccessibilityAccountData::RemoveConnectingA11yAbility(const std::string &uri)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->RemoveConnectingA11yAbility(uri);
    }
}

// For UT
void MockAccessibilityAccountData::AddEnabledAbility(const std::string& bundleName)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->AddEnabledAbility(bundleName);
    }
}

RetError MockAccessibilityAccountData::RemoveEnabledAbility(const std::string &name)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->RemoveEnabledAbility(name);
    }
    return RET_ERR_NOT_ENABLED;
}

// For UT
void MockAccessibilityAccountData::AddInstalledAbility(AccessibilityAbilityInfo& abilityInfo)
{
    (void)abilityInfo;
}

void MockAccessibilityAccountData::RemoveInstalledAbility(const std::string &bundleName)
{
    (void)bundleName;
}

void MockAccessibilityAccountData::ClearInstalledAbility()
{}

const sptr<AccessibleAbilityConnection> MockAccessibilityAccountData::GetAccessibleAbilityConnection(
    const std::string &elementName)
{
    (void)elementName;
    return nullptr;
}

const sptr<AccessibilityWindowConnection> MockAccessibilityAccountData::GetAccessibilityWindowConnection(
    const int32_t windowId)
{
    HILOG_DEBUG("Mock: GetAccessibilityWindowConnection windowId(%{public}d)", windowId);
    return operatorManager_.GetAccessibilityWindowConnection(windowId);
}

const std::map<std::string, sptr<AccessibleAbilityConnection>> MockAccessibilityAccountData::GetConnectedA11yAbilities()
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->GetConnectedA11yAbilities();
    }
    return {};
}

const std::map<int32_t, sptr<AccessibilityWindowConnection>> MockAccessibilityAccountData::GetAsacConnections()
{
    HILOG_DEBUG("Mock: GetAsacConnections start");
    return operatorManager_.GetAsacConnections();
}

const CaptionPropertyCallbacks MockAccessibilityAccountData::GetCaptionPropertyCallbacks()
{
    HILOG_DEBUG("GetCaptionPropertyCallbacks start");
    return captionPropertyCallbacks_;
}

sptr<AccessibleAbilityConnection> MockAccessibilityAccountData::GetConnectingA11yAbility(const std::string &uri)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->GetConnectingA11yAbility(uri);
    }
    return nullptr;
}

const std::vector<std::string>& MockAccessibilityAccountData::GetEnabledAbilities()
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->GetEnabledAbilities();
    }
    static std::vector<std::string> emptyVector;
    return emptyVector;
}

const std::vector<AccessibilityAbilityInfo> &MockAccessibilityAccountData::GetInstalledAbilities() const
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->GetInstalledAbilities();
    }
    static std::vector<AccessibilityAbilityInfo> emptyVector;
    return emptyVector;
}

void MockAccessibilityAccountData::GetAbilitiesByState(
    AbilityStateType state, std::vector<AccessibilityAbilityInfo> &abilities)
{
    (void)state;
    (void)abilities;
}

void MockAccessibilityAccountData::UpdateAccountCapabilities()
{}

void MockAccessibilityAccountData::UpdateEventTouchGuideCapability()
{}

void MockAccessibilityAccountData::UpdateGesturesSimulationCapability()
{}

void MockAccessibilityAccountData::UpdateFilteringKeyEventsCapability()
{
    isFilteringKeyEvents_ = false;
}

void MockAccessibilityAccountData::UpdateMagnificationCapability()
{
    isScreenMagnification_ = false;
}

void MockAccessibilityAccountData::UpdateEnableAbilityListsState()
{
    return;
}

RetError MockAccessibilityAccountData::EnableAbility(const std::string &name, const uint32_t capabilities)
{
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        return abilityManager->EnableAbility(name, capabilities);
    }
    return RET_ERR_INVALID_PARAM;
}

bool MockAccessibilityAccountData::GetInstalledAbilitiesFromBMS()
{
    HILOG_DEBUG("start.");
    return true;
}

void MockAccessibilityAccountData::SetScreenReaderState(const std::string &name, const std::string &state)
{
    HILOG_DEBUG("start.");
    (void)name;
    (void)state;
}

bool MockAccessibilityAccountData::GetDefaultUserScreenReaderState()
{
    HILOG_DEBUG("start.");
    return true;
}

bool MockAccessibilityAccountData::GetScreenReaderState()
{
    HILOG_DEBUG("start.");
    return true;
}

void MockAccessibilityAccountData::Init()
{
    HILOG_DEBUG("start.");
    if (!config_) {
        config_ = std::make_shared<AccessibilitySettingsConfig>(id_);
    }
    auto abilityManager = GetAbilityManager();
    if (abilityManager) {
        abilityManager->SetConfig(config_);
    }
}

void MockAccessibilityAccountData::AddEnableAbilityListsObserver(
    const sptr<IAccessibilityEnableAbilityListsObserver>& observer)
{
    HILOG_DEBUG("start.");
    (void)observer;
}

void MockAccessibilityAccountData::RemoveEnableAbilityListsObserver(const wptr<IRemoteObject>& observer)
{
    (void)observer;
}

void MockAccessibilityAccountData::AddConfigCallback(
    const sptr<IAccessibleAbilityManagerConfigObserver>& callback)
{
    HILOG_DEBUG("AddConfigCallback start");
    configCallbacks_.push_back(callback);
}

const std::vector<sptr<IAccessibleAbilityManagerConfigObserver>> MockAccessibilityAccountData::GetConfigCallbacks()
{
    HILOG_DEBUG("GetConfigCallbacks start");
    return configCallbacks_;
}

void MockAccessibilityAccountData::SetConfigCallbacks(std::vector<sptr<IAccessibleAbilityManagerConfigObserver>>& observer)
{
    HILOG_DEBUG("SetConfigCallbacks start");
    configCallbacks_ = observer;
}

void MockAccessibilityAccountData::RemoveConfigCallback(const wptr<IRemoteObject>& callback)
{
    HILOG_DEBUG("RemoveConfigCallback start");
    for (auto iter = configCallbacks_.begin(); iter != configCallbacks_.end(); iter++) {
        if ((*iter)->AsObject() == callback) {
            configCallbacks_.erase(iter);
            break;
        }
    }
}

std::shared_ptr<AccessibilitySettingsConfig> MockAccessibilityAccountData::GetConfig()
{
    HILOG_DEBUG("GetConfig start");
    return config_;
}

void MockAccessibilityAccountData::GetImportantEnabledAbilities(
    std::map<std::string, uint32_t> &importantEnabledAbilities) const
{
    HILOG_DEBUG("GetImportantEnabledAbilities start");
    (void)importantEnabledAbilities;
}

void MockAccessibilityAccountData::UpdateImportantEnabledAbilities(
    std::map<std::string, uint32_t> &importantEnabledAbilities)
{
    HILOG_DEBUG("UpdateImportantEnabledAbilities start");
    (void)importantEnabledAbilities;
}

uint32_t MockAccessibilityAccountData::GetInputFilterFlag() const
{
    HILOG_DEBUG("GetInputFilterFlag start");
    return 0;
}

void MockAccessibilityAccountData::UpdateAbilities()
{
    HILOG_DEBUG("UpdateAbilities start");
}

void MockAccessibilityAccountData::AddUITestClient(const sptr<IRemoteObject> &obj,
    const std::string &bundleName, const std::string &abilityName)
{
    (void)obj;
    (void)bundleName;
    (void)abilityName;
}

void MockAccessibilityAccountData::RemoveUITestClient(
    sptr<AccessibleAbilityConnection> &connection, const std::string &bundleName)
{
    (void)connection;
    (void)bundleName;
}

void MockAccessibilityAccountData::SetAbilityAutoStartState(const std::string &name, const bool state)
{
    (void)name;
    (void)state;
}

bool MockAccessibilityAccountData::GetAbilityAutoStartState(const std::string &key)
{
    (void)key;
    return true;
}

void MockAccessibilityAccountData::GetConfigValueAtoHos(ConfigValueAtoHosUpdate &value)
{
    (void)value;
}

void MockAccessibilityAccountData::DelAutoStartPrefKeyInRemovePkg(const std::string &bundleName)
{
    (void)bundleName;
}

void MockAccessibilityAccountData::UpdateAutoStartEnabledAbilities()
{
}

sptr<MockAccessibilityAccountData> MockAccessibilityAccountDataMap::AddAccountData(
    int32_t accountId)
{
    auto iter = accountDataMap_.find(accountId);
    if (iter != accountDataMap_.end()) {
        HILOG_DEBUG("accountId is existed");
        return iter->second;
    }

    sptr<MockAccessibilityAccountData> accountData = new(std::nothrow) MockAccessibilityAccountData(accountId);
    if (accountData == nullptr) {
        return nullptr;
    }

    accountData->Init();
    accountDataMap_[accountId] = accountData;
    return accountData;
}

sptr<MockAccessibilityAccountData> MockAccessibilityAccountDataMap::GetCurrentAccountData(
    int32_t accountId)
{
    auto iter = accountDataMap_.find(accountId);
    if (iter != accountDataMap_.end()) {
        return iter->second;
    }

    sptr<MockAccessibilityAccountData> accountData = new(std::nothrow) MockAccessibilityAccountData(accountId);
    if (!accountData) {
        return nullptr;
    }

    accountDataMap_[accountId] = accountData;
    return accountData;
}

sptr<MockAccessibilityAccountData> MockAccessibilityAccountDataMap::GetAccountData(
    int32_t accountId)
{
    auto iter = accountDataMap_.find(accountId);
    if (iter != accountDataMap_.end()) {
        return iter->second;
    }

    return nullptr;
}

sptr<MockAccessibilityAccountData> MockAccessibilityAccountDataMap::RemoveAccountData(
    int32_t accountId)
{
    sptr<MockAccessibilityAccountData> accountData = nullptr;
    auto iter = accountDataMap_.find(accountId);
    if (iter != accountDataMap_.end()) {
        accountData = iter->second;
        accountDataMap_.erase(iter);
    }

    return accountData;
}

void MockAccessibilityAccountDataMap::Clear()
{
    accountDataMap_.clear();
}

AccountSA::OsAccountType MockAccessibilityAccountData::GetAccountType()
{
    return AccountSA::OsAccountType::PRIVATE;
}

std::shared_ptr<AccessibilityAbilityManager> MockAccessibilityAccountData::GetAbilityManager() const
{
    std::lock_guard<ffrt::mutex> lock(abilityManagerMutex_);
    auto it = abilityManagerMap_.find(id_);
    if (it != abilityManagerMap_.end()) {
        return it->second;
    }
    return nullptr;
}

ErrCode MockAccessibilityAccountData::RegisterElementOperatorByWindowId(const uint32_t tokenId, const int32_t windowId,
    const sptr<IAccessibilityElementOperator>& elementOperator, bool isApp)
{
    (void)tokenId;
    (void)windowId;
    (void)elementOperator;
    (void)isApp;
    return RET_OK;
}

ErrCode MockAccessibilityAccountData::RegisterElementOperatorByParameter(const RegistrationPara& parameter,
    const sptr<IAccessibilityElementOperator>& elementOperator)
{
    (void)parameter;
    (void)elementOperator;
    return RET_OK;
}

ErrCode MockAccessibilityAccountData::DeregisterElementOperatorByWindowId(const int32_t windowId)
{
    (void)windowId;
    return RET_OK;
}

ErrCode MockAccessibilityAccountData::DeregisterElementOperatorByWindowIdAndTreeId(const int32_t windowId,
     const int32_t treeId)
{
    (void)windowId;
    (void)treeId;
    return RET_OK;
}

RetError MockAccessibilityAccountData::VerifyingToKenId(const uint32_t tokenId, const int32_t windowId,
    const int64_t elementId)
{
    (void)tokenId;
    (void)windowId;
    (void)elementId;
    return RET_OK;
}

ElementOperatorManager& MockAccessibilityAccountData::GetOperatorManager()
{
    static ElementOperatorManager mockOperatorManager;
    return mockOperatorManager;
}
} // namespace Accessibility
} // namespace OHOS