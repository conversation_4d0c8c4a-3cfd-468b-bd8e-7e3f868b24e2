/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import AccessibilityIpcTypes;
 
sequenceable parcel.accessibility_ability_info_parcel..OHOS.Accessibility.AccessibilityAbilityInfoParcel;
sequenceable parcel.accessibility_caption_parcel..OHOS.Accessibility.CaptionPropertyParcel;
sequenceable parcel.accessibility_event_info_parcel..OHOS.Accessibility.AccessibilityEventInfoParcel;
interface OHOS.Accessibility.IAccessibleAbilityManagerStateObserver;
interface OHOS.Accessibility.IAccessibleAbilityManagerCaptionObserver;
interface OHOS.Accessibility.IAccessibilityEnableAbilityListsObserver;
interface OHOS.Accessibility.IAccessibilityElementOperator;
interface OHOS.Accessibility.IAccessibleAbilityManagerConfigObserver;
sequenceable OHOS.IRemoteObject;
rawdata OHOS.Accessibility.AccessibilitySecCompRawdata;

interface OHOS.Accessibility.IAccessibleAbilityManagerService {
    [oneway] void SendEvent([in] AccessibilityEventInfoParcel eventInfoParcel, [in] int flag);
    void RegisterStateObserver([in] IAccessibleAbilityManagerStateObserver observer, [out] unsigned int state);
    void RegisterCaptionObserver([in] IAccessibleAbilityManagerCaptionObserver observer);
    [oneway] void RegisterEnableAbilityListsObserver([in] IAccessibilityEnableAbilityListsObserver observer);
    void GetAbilityList([in] unsigned int abilityTypes, [in] int stateType, [out] AccessibilityAbilityInfoParcel[] infos);
    [oneway] void RegisterElementOperatorByWindowId([in] int windowId, [in] IAccessibilityElementOperator elementOperator);
    [oneway] void RegisterElementOperatorByParameter([in] RegistrationPara parameter, [in] IAccessibilityElementOperator elementOperator);
    [oneway] void DeregisterElementOperatorByWindowId([in] int windowId);
    [oneway] void DeregisterElementOperatorByWindowIdAndTreeId([in] int windowId, [in] int treeId);
    void GetCaptionProperty([out] CaptionPropertyParcel caption);
    void GetCaptionState([out] boolean state);
    void SetCaptionProperty([in] CaptionPropertyParcel caption);
    void SetCaptionState([in] boolean state);
    void EnableAbility([in] String name, [in] unsigned int capabilities);
    void GetEnabledAbilities([out] String[] enabledAbilities);
    void DisableAbility([in] String name);
    void SetMagnificationState([in] boolean state);
    void GetActiveWindow([out] int windowId);
    void GetActiveWindow([out] int windowId, [in] boolean systemApi);
    void CheckExtensionAbilityPermission([inout] String processName);
    void EnableUITestAbility([in] IRemoteObject obj);
    void DisableUITestAbility();
    void SetScreenMagnificationState([in] boolean state);
    void SetShortKeyState([in] boolean state);
    void SetMouseKeyState([in] boolean state);
    void SetMouseAutoClick([in] int time);
    void SetShortkeyTarget([in] String name);
    void SetShortkeyMultiTarget([in] String[] name);
    void SetHighContrastTextState([in] boolean state);
    void SetInvertColorState([in] boolean state);
    void SetAnimationOffState([in] boolean state);
    void SetAudioMonoState([in] boolean state);
    void SetDaltonizationState([in] boolean state);
    void SetDaltonizationColorFilter([in] unsigned int filter);
    void SetContentTimeout([in] unsigned int time);
    void SetBrightnessDiscount([in] float discount);
    void SetAudioBalance([in] float balance);
    void SetClickResponseTime([in] unsigned int time);
    void SetIgnoreRepeatClickState([in] boolean state);
    void SetIgnoreRepeatClickTime([in] unsigned int time);
    void GetScreenMagnificationState([out] boolean state);
    void GetShortKeyState([out] boolean state);
    void GetMouseKeyState([out] boolean state);
    void GetMouseAutoClick([out] int time);
    void GetShortkeyTarget([out] String name);
    void GetShortkeyMultiTarget([out] String[] name);
    void GetHighContrastTextState([out] boolean state);
    void GetInvertColorState([out] boolean state);
    void GetAnimationOffState([out] boolean state);
    void GetAudioMonoState([out] boolean state);
    void GetDaltonizationState([out] boolean state);
    void GetDaltonizationColorFilter([out] unsigned int filter);
    void GetContentTimeout([out] unsigned int time);
    void GetBrightnessDiscount([out] float brightness);
    void GetAudioBalance([out] float balance);
    void GetClickResponseTime([out] unsigned int time);
    void GetIgnoreRepeatClickState([out] boolean state);
    void GetIgnoreRepeatClickTime([out] unsigned int time);
    void GetAllConfigs([out] AccessibilityConfigData configData, [out] CaptionPropertyParcel caption);
    void GetRealWindowAndElementId([inout] int windowId, [inout] long elementId);
    void GetSceneBoardInnerWinId([in] int windowId, [in] long elementId, [out] int innerWid);
    void GetFocusedWindowId([out] int focusedWindowId);
    void RegisterConfigObserver([in] IAccessibleAbilityManagerConfigObserver observer);
    void RemoveRequestId([in] int requestId);
    void GetRootParentId([in] int windowsId, [in] int treeId, [out] long parentId);
    void GetRootParentId([in] int windowsId, [in] int treeId, [out] long parentId, [in] boolean systemApi);
    void GetScreenReaderState([out] boolean state);
    void SetEnhanceConfig([in] AccessibilitySecCompRawdata rawData);
    void SearchNeedEvents([out] unsigned int[] needEvents);
}