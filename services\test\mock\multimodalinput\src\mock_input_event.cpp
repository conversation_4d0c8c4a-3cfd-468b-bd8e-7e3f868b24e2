/*
 * Copyright (C) 2022 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#define private public
#define protected public
#include "input_event.h"
#undef private
#undef protected
#include "mock_input_event.h"

namespace OHOS {
namespace MMI {
InputEvent::InputEvent(int32_t eventType) : eventType_(eventType)
{
    Reset();
}

InputEvent::InputEvent(const InputEvent& other)
    : eventType_(other.eventType_),
      id_(other.id_),
      actionTime_(other.actionTime_),
      action_(other.action_),
      actionStartTime_(other.actionStartTime_),
      deviceId_(other.deviceId_),
      sourceType_(other.sourceType_),
      targetDisplayId_(other.targetDisplayId_),
      targetWindowId_(other.targetWindowId_),
      agentWindowId_(other.agentWindowId_),
      bitwise_(other.bitwise_),
      processedCallback_(other.processedCallback_)
{}

InputEvent::~InputEvent()
{}

void InputEvent::Reset()
{}

std::string InputEvent::ToString()
{
    return "";
}

std::shared_ptr<InputEvent> InputEvent::Create()
{
    return std::make_shared<InputEvent>(InputEvent::EVENT_TYPE_BASE);
}

int32_t InputEvent::GetId() const
{
    return id_;
}

void InputEvent::SetId(int32_t id)
{
    id_ = id;
}

void InputEvent::UpdateId()
{}

int64_t InputEvent::GetActionTime() const
{
    return actionTime_;
}

void InputEvent::SetActionTime(int64_t actionTime)
{
    actionTime_ = actionTime;
}

int32_t InputEvent::GetAction() const
{
    return action_;
}

void InputEvent::SetAction(int32_t action)
{
    action_ = action;
}

int64_t InputEvent::GetActionStartTime() const
{
    return actionStartTime_;
}

void InputEvent::SetActionStartTime(int64_t actionStartTime)
{
    actionStartTime_ = actionStartTime;
}

int32_t InputEvent::GetSourceType() const
{
    return sourceType_;
}

void InputEvent::SetSourceType(int32_t sourceType)
{
    sourceType_ = sourceType;
}

uint32_t InputEvent::GetFlag() const
{
    return bitwise_;
}

bool InputEvent::HasFlag(uint32_t flag)
{
    return (bitwise_ & flag) != 0;
}

void InputEvent::AddFlag(uint32_t flag)
{
    bitwise_ |= flag;
}

void InputEvent::ClearFlag()
{
    bitwise_ = EVENT_FLAG_NONE;
}
} // namespace MMI
} // namespace OHOS