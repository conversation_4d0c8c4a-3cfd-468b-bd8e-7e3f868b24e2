# Copyright (C) 2022-2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/test.gni")

module_output_path = "accessibility/accessibility"

###############################################################################
config("module_private_config") {
  visibility = [ ":*" ]
  cflags = []
  if (target_cpu == "arm") {
    cflags += [ "-DBINDER_IPC_32BIT" ]
  }
  include_dirs = [
    "../../../common/log/include",
    "../../aafwk/include",
    "../../aafwk/test/mock/include",
    "../../acfwk/include",
    "../../asacfwk/include",
    "../../asacfwk/test/mock/include",
    "../../../interfaces/innerkits/acfwk/include",
    "../../../interfaces/innerkits/asacfwk/include",
    "../../../services/test/mock/common",
  ]
  defines = [
    "AAMS_LOG_DOMAIN = 0xD001D05",
    "AAMS_LOG_TAG = \"accessibility_test\"",
  ]
}

ohos_unittest("asac_unit_test") {
  module_out_path = module_output_path
  sources = [
    "../../../services/test/mock/mock_accessible_ability_manager_service_stub.cpp",
    "../../../services/test/mock/mock_bundle_manager.cpp",
    "../../../services/test/mock/mock_parameter.c",
    "../../../services/test/mock/mock_service_registry.cpp",
    "../../../common/interface/src/api_event_reporter.cpp",
    "../../aafwk/src/accessibility_element_operator_callback_impl.cpp",
    "../src/accessibility_element_operator_impl.cpp",
    "../src/accessibility_system_ability_client_impl.cpp",
    "unittest/accessibility_element_operator_impl_test.cpp",
    "unittest/accessibility_system_ability_client_impl_test.cpp",
  ]

  configs = [
    ":module_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:want",
    "ability_base:zuri",
    "ability_runtime:ability_manager",
    "ability_runtime:abilitykit_native",
    "ability_runtime:dataobs_manager",
    "ability_runtime:extension_manager",
    "bundle_framework:appexecfwk_base",
    "bundle_framework:appexecfwk_core",
    "c_utils:utils",
    "data_share:datashare_common",
    "data_share:datashare_consumer",
    "e2fsprogs:libext2_uuid",
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "googletest:gmock_main",
    "googletest:gtest_main",
    "hilog:libhilog",
    "init:libbegetutil",
    "ipc:ipc_core",
    "samgr:dynamic_cache",
    "samgr:samgr_proxy",
  ]

  if (product_name != "qemu-arm-linux-min") {
    external_deps += [ "hiappevent:hiappevent_innerapi" ]
  }
}

###############################################################################
group("unittest") {
  testonly = true
  deps = []

  deps += [ ":asac_unit_test" ]
}
###############################################################################
