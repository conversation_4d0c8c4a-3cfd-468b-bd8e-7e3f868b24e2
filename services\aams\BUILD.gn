# Copyright (C) 2022-2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("../../accessibility_manager_service.gni")

services_path = "./"

ohos_prebuilt_etc("accessibility_service.rc") {
  source = "etc/accessibility_service.rc"
  relative_install_dir = "init"
  part_name = "accessibility"
  subsystem_name = "barrierfree"
}

config("aams_service_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "include",
    "../../common/log/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility\"",
    "AAMS_LOG_DOMAIN = 0xD001D00",
  ]

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }

  defines += accessibility_default_defines
}

accessibleabilityms_files = [
  "${services_path}/src/accessible_ability_channel.cpp",
  "${services_path}/src/accessible_ability_connection.cpp",
  "${services_path}/src/accessible_ability_manager_service_event_handler.cpp",
  "${services_path}/src/accessible_ability_manager_service.cpp",
  "${services_path}/src/accessibility_account_data.cpp",
  "${services_path}/src/accessibility_circle_drawing_manager.cpp",
  "${services_path}/src/accessibility_settings.cpp",
  "${services_path}/src/accessibility_settings_config.cpp",
  "${services_path}/src/accessibility_window_connection.cpp",
  "${services_path}/src/accessibility_zoom_gesture.cpp",
  "${services_path}/src/accessibility_common_event.cpp",
  "${services_path}/src/accessibility_event_transmission.cpp",
  "${services_path}/src/accessibility_input_interceptor.cpp",
  "${services_path}/src/accessibility_keyevent_filter.cpp",
  "${services_path}/src/accessibility_mouse_autoclick.cpp",
  "${services_path}/src/accessibility_mouse_key.cpp",
  "${services_path}/src/accessibility_screen_touch.cpp",
  "${services_path}/src/accessibility_short_key.cpp",
  "${services_path}/src/accessibility_touchEvent_injector.cpp",
  "${services_path}/src/accessibility_window_manager.cpp",
  "${services_path}/src/accessibility_dumper.cpp",
  "${services_path}/src/accessibility_resource_bundle_manager.cpp",
  "${services_path}/src/accessibility_setting_observer.cpp",
  "${services_path}/src/accessibility_setting_provider.cpp",
  "${services_path}/src/accessibility_notification_helper.cpp",
  "${services_path}/src/msdp_manager.cpp",
  "${services_path}/src/accessibility_security_component_manager.cpp",
  "${services_path}/src/accessibility_short_key_dialog.cpp",
  "${services_path}/src/accessibility_datashare_helper.cpp",
  "${services_path}/src/touch_exploration_multi_finger_gesture.cpp",
  "${services_path}/src/touch_exploration_single_finger_gesture.cpp",
  "${services_path}/src/utils.cpp",
  "${services_path}/src/magnification_manager.cpp",
  "${services_path}/src/window_magnification_manager.cpp",
  "${services_path}/src/window_magnification_gesture.cpp",
  "${services_path}/src/magnification_menu_manager.cpp",
  "${services_path}/src/full_screen_magnification_manager.cpp",
  "${services_path}/src/magnification_window_proxy.cpp",
  "${services_path}/src/accessibility_ability_manager.cpp",
  "${services_path}/src/element_operator_manager.cpp",
]

ohos_shared_library("accessibleabilityms") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }

  sources = accessibleabilityms_files
  if (accessibility_feature_power_manager) {
    sources += [ "${services_path}/src/accessibility_power_manager.cpp" ]
  }

  if (accessibility_feature_display_manager) {
    sources += [ "${services_path}/src/accessibility_display_manager.cpp" ]
  }

  configs = [
    ":aams_service_config",
    "../../resources/config/build:coverage_flags",
  ]

  version_script = "libaccessibilityms.map"

  deps = [
    "../../common/interface:accessibility_interface",
    "../../interfaces/innerkits/common:accessibility_common",
  ]

  external_deps = [
    "ability_base:want",
    "ability_base:zuri",
    "ability_base:configuration",
    "ability_runtime:ability_connect_callback_stub",
    "ability_runtime:ability_manager",
    "ability_runtime:abilitykit_native",
    "ability_runtime:extension_manager",
    "ability_runtime:app_manager",
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "bundle_framework:appexecfwk_core",
    "c_utils:utils",
    "common_event_service:cesfwk_innerkits",
    "eventhandler:libeventhandler",
    "ffrt:libffrt",
    "graphic_2d:2d_graphics",
    "graphic_2d:librender_service_client",
    "graphic_2d:librender_service_base",
    "hicollie:libhicollie",
    "hilog:libhilog",
    "i18n:intl_util",
    "init:libbeget_proxy",
    "init:libbegetutil",
    "input:libmmi-client",
    "ipc:ipc_single",
    "memmgr:memmgrclient",
    "os_account:os_account_innerkits",
    "resource_management:global_resmgr",
    "safwk:system_ability_fwk",
    "samgr:samgr_proxy",
    "window_manager:libdm",
  ]

  if (accessibility_feature_power_manager) {
    external_deps += [ "power_manager:powermgr_client" ]
  }

  if (accessibility_feature_display_manager) {
    external_deps += [ "display_manager:displaymgr" ]
  }

  if (accessibility_feature_data_share) {
    external_deps += [ "data_share:datashare_consumer" ]
  }

  if (accessibility_feature_hiviewdfx_hitrace) {
    external_deps += [ "hitrace:hitrace_meter" ]
  }

  if (accessibility_feature_hiviewdfx_hisysevent) {
    external_deps += [ "hisysevent:libhisysevent" ]
  }

  if (accessibility_watch_feature) {
    external_deps += [ "resource_schedule_service:ressched_client" ]
  }

  if (security_component_enable) {
    external_deps += [ "security_component_manager:libsecurity_component_sdk" ]
  }

  install_enable = true

  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
