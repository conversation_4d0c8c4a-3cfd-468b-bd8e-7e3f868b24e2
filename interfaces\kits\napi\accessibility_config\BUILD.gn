# Copyright (C) 2022-2025 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("../../../../accessibility_manager_service.gni")

ohos_shared_library("config_napi") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = false
    cfi_cross_dso = false
    debug = false
  }

  include_dirs = [
    "../../../../common/log/include",
    "../include",
    "./include",
    "../../../../common/interface/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"accessibility_napi\"",
    "AAMS_LOG_DOMAIN = 0xD001D10",
  ]

  defines += accessibility_default_defines

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }

  sources = [
    "../src/napi_accessibility_utils.cpp",
    "./src/napi_accessibility_config_observer.cpp",
    "./src/napi_accessibility_config.cpp",
    "./src/native_module.cpp",
    "../../../../common/interface/src/api_event_reporter.cpp",
  ]

  configs = [ "../../../../resources/config/build:coverage_flags" ]

  deps = [
    "../../../../common/interface:accessibility_interface",
    "../../../innerkits/acfwk:accessibilityconfig",
    "./../../../innerkits/common:accessibility_common",
  ]

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "e2fsprogs:libext2_uuid",
    "ffrt:libffrt",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_core",
    "napi:ace_napi",
  ]

  if (product_name != "qemu-arm-linux-min") {
    external_deps += [ "hiappevent:hiappevent_innerapi" ]
  }

  relative_install_dir = "module/accessibility"
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
