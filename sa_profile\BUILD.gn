# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//build/ohos/sa_profile/sa_profile.gni")
import("//foundation/barrierfree/accessibility/accessibility_manager_service.gni")

ohos_sa_profile("aams_sa_profile") {
  if (!accessibility_dynamic_support) {
    sources = [ "801.json" ]
  } else {
    sources = [ "801_dynamic.json" ]
  }

  part_name = "accessibility"
}

ohos_prebuilt_etc("accessibility_cfg") {
  source = "accessibility.cfg"
  relative_install_dir = "init"
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
