# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

cj_accessibility_path = "./"

config("cj_accessibility_private_config") {
  visibility = [ ":*" ]

  include_dirs = [
    "${cj_accessibility_path}/include",
    "../../../common/log/include",
    "../../../interfaces/innerkits/asacfwk/include",
    "../../../interfaces/innerkits/common/include",
  ]

  defines = [
    "AAMS_LOG_TAG = \"cj_accessibility_ffi\"",
    "AAMS_LOG_DOMAIN = 0xD001D05",
  ]

  if (build_variant == "user") {
    defines += [ "RELEASE_VERSION" ]
  }
}

config("cj_accessibility_ffi_public_config") {
  include_dirs = [ "${cj_accessibility_path}/include" ]
}

cj_accessibility_ffi_src = [
  "${cj_accessibility_path}/src/cj_accessibility_ffi.cpp",
  "${cj_accessibility_path}/src/cj_accessibility_utils.cpp",
  "${cj_accessibility_path}/src/cj_accessibility_callback.cpp",
]

ohos_shared_library("cj_accessibility_ffi") {
  branch_protector_ret = "pac_ret"
  sanitize = {
    integer_overflow = true
    ubsan = true
    boundary_sanitize = true
    cfi = true
    cfi_cross_dso = true
    debug = false
  }

  sources = cj_accessibility_ffi_src
  configs = [
    ":cj_accessibility_private_config",
    "../../../resources/config/build:coverage_flags",
  ]

  public_configs = [ ":cj_accessibility_ffi_public_config" ]

  deps = [
    "../../../common/interface:accessibility_interface",
    "../../innerkits/acfwk:accessibilityconfig",
    "../../innerkits/asacfwk:accessibilityclient",
    "../../innerkits/common:accessibility_common",
  ]

  install_enable = true

  external_deps = [
    "access_token:libaccesstoken_sdk",
    "access_token:libtokenid_sdk",
    "c_utils:utils",
    "hilog:libhilog",
    "input:libmmi-client",
    "ipc:ipc_single",
    "napi:ace_napi",
    "napi:cj_bind_ffi",
    "napi:cj_bind_native",
  ]
  innerapi_tags = [ "platformsdk" ]
  subsystem_name = "barrierfree"
  part_name = "accessibility"
}
