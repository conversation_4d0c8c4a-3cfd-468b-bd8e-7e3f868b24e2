# Copyright (C) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

AAMS_MOCK_PATH = "//foundation/barrierfree/accessibility/services/test/mock"
AAMS_FRAMEWORKS_PATH = "//foundation/barrierfree/accessibility/frameworks"
AAMS_INTERFACES_PATH = "//foundation/barrierfree/accessibility/interfaces"
AAMS_SERVICES_PATH = "//foundation/barrierfree/accessibility/services"
AAMS_COMMON_PATH = "//foundation/barrierfree/accessibility/common"
AAMS_FOUNDATION_PATH = "//foundation"

aams_mock_include_dirs = [
  "$AAMS_MOCK_PATH/distributeddatamgr/include",
  "$AAMS_MOCK_PATH/multimodalinput/include",
  "$AAMS_MOCK_PATH/powermanager/include",
]

aams_mock_distributeddatamgr_src =
    [ "$AAMS_MOCK_PATH/distributeddatamgr/src/mock_preferences_helper.cpp" ]

aams_mock_multimodalinput_src = [
  "$AAMS_MOCK_PATH/multimodalinput/src/mock_input_event.cpp",
  "$AAMS_MOCK_PATH/multimodalinput/src/mock_input_manager.cpp",
  "$AAMS_MOCK_PATH/multimodalinput/src/mock_key_event.cpp",
  "$AAMS_MOCK_PATH/multimodalinput/src/mock_pointer_event.cpp",
]

aams_mock_powermanager_src =
    [ "$AAMS_MOCK_PATH/powermanager/src/mock_power_mgr_client.cpp" ]
